version: '3.8'
services:
  postgres:
    image: postgres:16
    container_name: postgres_db
    restart: always
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - b2b-network
      - traefik-proxy
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "${POSTGRES_USER}","-d", "${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS}
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"
      - "15672:15672" 
    networks:
      - b2b-network
      - traefik-proxy
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_running"]
      interval: 30s
      timeout: 30s
      retries: 5
      start_period: 60s

  pgadmin:
    image: dpage/pgadmin4:9.1
    container_name: pgadmin
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD}
      PGADMIN_LISTEN_PORT: 80
    ports:
      - "5050:80"  
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - b2b-network
      - traefik-proxy

  api-gateway:
    build:
      context: ./B2B.Backend
      dockerfile: ApiGateway/Dockerfile
    container_name: api_gateway
    restart: always
    env_file:
      - .env
    environment:
      - ASPNETCORE_URLS=http://+:33800
    networks:
      - b2b-network
      - traefik-proxy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-gateway.rule=Host(`futureapi.ukscreative.com`)"
      - "traefik.http.routers.api-gateway.tls=true"
      - "traefik.http.routers.api-gateway.tls.certresolver=letsencrypt"
      - "traefik.http.services.api-gateway.loadbalancer.server.port=33800"
      - "traefik.docker.network=traefik-proxy"

  panel-api:
    build:
      context: ./B2B.Backend
      dockerfile: PanelApi/Dockerfile
    container_name: panel_api
    restart: always
    env_file:
      - .env
    environment:
      - ASPNETCORE_URLS=http://+:5000
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - b2b-network

  web-api:
    build:
      context: ./B2B.Backend
      dockerfile: WebApi/Dockerfile
    container_name: web_api
    restart: always
    env_file:
      - .env
    environment:
      - ASPNETCORE_URLS=http://+:5000
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - b2b-network

  media-api:
    build:
      context: ./B2B.Backend
      dockerfile: MediaAPI/Dockerfile
    container_name: media_api
    restart: always
    env_file:
      - .env
    environment:
      - ASPNETCORE_URLS=http://+:5000
      - StorageSettings__BaseUrl=https://futurecdn.ukscreative.com/images
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    volumes:
      - media_data:/app/wwwroot
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.media-api.rule=Host(`futurecdn.ukscreative.com`)"
      - "traefik.http.routers.media-api.tls=true"
      - "traefik.http.routers.media-api.tls.certresolver=letsencrypt"
      - "traefik.http.services.media-api.loadbalancer.server.port=5000"
      - "traefik.docker.network=traefik-proxy"
    networks:
      - b2b-network
      - traefik-proxy

  mail-api:
    build:
      context: ./B2B.Backend
      dockerfile: MailAPI/Dockerfile
    container_name: mail_api
    restart: always
    env_file:
      - .env
    environment:
      - ASPNETCORE_URLS=http://+:5000
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - b2b-network

  b2b-frontend:
    build:
      context: ./b2b.frontend
      dockerfile: Dockerfile
      args:
        - CI=false
    container_name: b2b_frontend
    restart: always
    environment:
      - NODE_ENV=production
      - HOSTNAME=0.0.0.0
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_GATEWAY_URL}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - CI=false
    depends_on:
      - api-gateway
    networks:
      - b2b-network
      - traefik-proxy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.b2b-frontend.rule=Host(`futurepanel.ukscreative.com`)"
      - "traefik.http.routers.b2b-frontend.tls=true"
      - "traefik.http.routers.b2b-frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.b2b-frontend.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik-proxy"

  vineta-theme:
    build:
      context: ./themes/vineta
      dockerfile: Dockerfile
      args:
        - CI=false
    container_name: vineta_theme
    restart: always
    environment:
      - NODE_ENV=production
      - HOSTNAME=0.0.0.0
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_GATEWAY_URL}
      - CI=false
    depends_on:
      - api-gateway
    networks:
      - b2b-network
      - traefik-proxy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vineta-theme.rule=Host(`future.ukscreative.com`)"
      - "traefik.http.routers.vineta-theme.tls=true"
      - "traefik.http.routers.vineta-theme.tls.certresolver=letsencrypt"
      - "traefik.http.services.vineta-theme.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik-proxy"

  worker-service:
    build:
      context: ./B2B.Backend
      dockerfile: WorkerService/Dockerfile
    container_name: worker_service
    restart: always
    env_file:
      - .env
    environment:
      - ASPNETCORE_URLS=http://+:5000
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - b2b-network
volumes:
  postgres_data:
  media_data:

networks:
  b2b-network:
    driver: bridge
  traefik-proxy:
    external: true
