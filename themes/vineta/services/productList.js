import { api } from '@/lib/api/client';
import { serverApi } from '@/lib/api/server';
import { withCache, getFromCache, setInCache } from '@/lib/cache/strategies';

/**
 * B2C Customer Product List API Services
 * Ürün listesi, filtreleme, say<PERSON>lama ve arama işlemleri için servisler
 */

// Unified ürün listesi - tüm sayfa türleri için (/urunler, /kategoriler, /yeni-sezon, /indirim)
export const getProductList = async (filters = {}) => {
  try {
    // Filter parametrelerini API formatına dönüştür
    const params = buildFilterParams(filters);
    return await api.get('/product', params);
  } catch (error) {
    console.error('Error fetching product list:', error);
    throw error;
  }
};

// Server-side rendering için ürün listesi (with cache)
export const getProductListSSR = async (filters = {}) => {
  try {
    const params = buildFilterParams(filters);
    return await serverApi.get('/product', params);
  } catch (error) {
    console.error('Error fetching product list (SSR):', error);
    throw error;
  }
};

// Kategori sayfası için ürün listesi
export const getCategoryProductList = async (categorySlug, filters = {}) => {
  try {
    const params = buildFilterParams(filters);
    return await api.get(`/category/${categorySlug}/products`, params);
  } catch (error) {
    console.error('Error fetching category product list:', error);
    throw error;
  }
};

// Server-side rendering için kategori ürün listesi
export const getCategoryProductListSSR = async (categorySlug, filters = {}) => {
  try {
    const params = buildFilterParams(filters);
    return await serverApi.get(`/category/${categorySlug}/products`, params);
  } catch (error) {
    console.error('Error fetching category product list (SSR):', error);
    throw error;
  }
};

// Ürün arama - gelişmiş filtreleme ile
export const searchProductList = async (searchRequest) => {
  try {
    return await api.get('/product/search', searchRequest);
  } catch (error) {
    console.error('Error searching product list:', error);
    throw error;
  }
};

// Server-side rendering için ürün arama
export const searchProductListSSR = async (searchRequest) => {
  try {
    return await serverApi.get('/product/search', searchRequest);
  } catch (error) {
    console.error('Error searching product list (SSR):', error);
    throw error;
  }
};

// Yeni sezon ürünleri listesi
export const getNewSeasonProductList = async (filters = {}) => {
  try {
    const params = { ...buildFilterParams(filters), pageType: 'new-season' };
    return await api.get('/product', params);
  } catch (error) {
    console.error('Error fetching new season product list:', error);
    throw error;
  }
};

// Server-side rendering için yeni sezon ürünleri
export const getNewSeasonProductListSSR = async (filters = {}) => {
  try {
    const params = { ...buildFilterParams(filters), pageType: 'new-season' };
    return await serverApi.get('/product', params);
  } catch (error) {
    console.error('Error fetching new season product list (SSR):', error);
    throw error;
  }
};

// İndirimli ürünler listesi
export const getDiscountedProductList = async (filters = {}) => {
  try {
    const params = { ...buildFilterParams(filters), pageType: 'discounted' };
    return await api.get('/product', params);
  } catch (error) {
    console.error('Error fetching discounted product list:', error);
    throw error;
  }
};

// Server-side rendering için indirimli ürünler
export const getDiscountedProductListSSR = async (filters = {}) => {
  try {
    const params = { ...buildFilterParams(filters), pageType: 'discounted' };
    return await serverApi.get('/product', params);
  } catch (error) {
    console.error('Error fetching discounted product list (SSR):', error);
    throw error;
  }
};

// Filter seçeneklerini getir
export const getProductListFilters = async (pageType = null, categorySlug = null) => {
  try {
    const params = {};
    if (pageType) params.pageType = pageType;
    if (categorySlug) params.categorySlug = categorySlug;

    return await api.get('/product/filters', params);
  } catch (error) {
    console.error('Error fetching product list filters:', error);
    throw error;
  }
};

// Server-side rendering için filter seçenekleri (with cache)
export const getProductListFiltersSSR = async (pageType = null, categorySlug = null) => {
  try {
    const cacheParams = { pageType, categorySlug };
        const params = {};
        if (pageType) params.pageType = pageType;
        if (categorySlug) params.categorySlug = categorySlug;

        return await serverApi.get('/product/filters', params);
  } catch (error) {
    console.error('Error fetching product list filters (SSR):', error);
    throw error;
  }
};

/**
 * Filter parametrelerini API formatına dönüştürür
 * Frontend filter state'ini backend API parametrelerine çevirir
 */
const buildFilterParams = (filters) => {
  const params = {};

  // Sayfalama
  if (filters.page) params.page = filters.page;
  if (filters.pageSize) params.pageSize = filters.pageSize;
  if (filters.limit) params.limit = filters.limit;

  // Sıralama
  if (filters.sort) params.sort = filters.sort;
  if (filters.sortBy) params.sortBy = filters.sortBy;
  if (filters.sortOrder) params.sortOrder = filters.sortOrder;

  // Kategori filtreleri
  if (filters.category) {
    params.category = Array.isArray(filters.category)
      ? filters.category.join(',')
      : filters.category;
  }
  if (filters.categories) {
    params.categories = Array.isArray(filters.categories)
      ? filters.categories.join(',')
      : filters.categories;
  }

  // Marka filtreleri
  if (filters.brand) {
    params.brand = Array.isArray(filters.brand)
      ? filters.brand.join(',')
      : filters.brand;
  }
  if (filters.brands) {
    params.brands = Array.isArray(filters.brands)
      ? filters.brands.join(',')
      : filters.brands;
  }

  // // Boyut filtreleri
  // if (filters.size) {
  //   params.size = Array.isArray(filters.size)
  //     ? filters.size.join(',')
  //     : filters.size;
  // }
  // if (filters.sizes) {
  //   params.sizes = Array.isArray(filters.sizes)
  //     ? filters.sizes.join(',')
  //     : filters.sizes;
  // }

  // // Renk filtreleri
  // if (filters.color) {
  //   params.color = Array.isArray(filters.color)
  //     ? filters.color.join(',')
  //     : filters.color;
  // }
  // if (filters.colors) {
  //   params.colors = Array.isArray(filters.colors)
  //     ? filters.colors.join(',')
  //     : filters.colors;
  // }
  //
  // Fiyat filtreleri
  if (filters.priceMin || filters.price_min) params.priceMin = filters.priceMin || filters.price_min;
  if (filters.priceMax || filters.price_max) params.priceMax = filters.priceMax || filters.price_max;
  if (filters.priceRange) {
    if (Array.isArray(filters.priceRange) && filters.priceRange.length === 2) {
      params.priceMin = filters.priceRange[0];
      params.priceMax = filters.priceRange[1];
    }
  }

  // Stok durumu
  if ((filters.inStock !== undefined && filters.inStock !== null) || filters.in_stock !== undefined) {
    params.inStock = filters.inStock !== undefined ? filters.inStock : filters.in_stock;
  }

  // İndirim durumu
  if (filters.onSale !== undefined || filters.discounted !== undefined) {
    params.onSale = filters.onSale !== undefined ? filters.onSale : filters.discounted;
  }

  // Arama sorgusu
  if (filters.query || filters.search) {
    params.query = filters.query || filters.search;
  }

  // Sayfa türü (new-season, discounted, featured, vb.)
  if (filters.pageType) params.pageType = filters.pageType;

  // Rating filtresi
  if (filters.rating || filters.minRating) {
    params.minRating = filters.rating || filters.minRating;
  }

  return params;
};

/**
 * URL query parametrelerini filter objesine dönüştürür
 * Browser URL'sindeki parametreleri frontend filter state'ine çevirir
 */
export const parseUrlFilters = (searchParams) => {
  const filters = {};

  // Sayfalama
  if (searchParams.get('page')) filters.page = parseInt(searchParams.get('page'));
  if (searchParams.get('limit')) filters.limit = parseInt(searchParams.get('limit'));

  // Sıralama
  if (searchParams.get('sort')) filters.sort = searchParams.get('sort');

  // Kategori
  if (searchParams.get('category')) {
    filters.category = searchParams.get('category').split(',');
  }

  // Marka
  if (searchParams.get('brand')) {
    filters.brand = searchParams.get('brand').split(',');
  }

  // Boyut
  if (searchParams.get('size')) {
    filters.size = searchParams.get('size').split(',');
  }

  // Renk
  if (searchParams.get('color')) {
    filters.color = searchParams.get('color').split(',');
  }

  // Fiyat
  if (searchParams.get('price_min')) filters.priceMin = parseFloat(searchParams.get('price_min'));
  if (searchParams.get('price_max')) filters.priceMax = parseFloat(searchParams.get('price_max'));

  // Stok
  if (searchParams.get('in_stock')) filters.inStock = searchParams.get('in_stock') === 'true';

  // İndirim
  if (searchParams.get('on_sale')) filters.onSale = searchParams.get('on_sale') === 'true';

  // Arama
  if (searchParams.get('q')) filters.query = searchParams.get('q');

  return filters;
};

/**
 * Filter objesini URL query parametrelerine dönüştürür
 * Frontend filter state'ini browser URL parametrelerine çevirir
 */
export const buildUrlParams = (filters) => {
  const params = new URLSearchParams();

  // Sayfalama
  if (filters.page && filters.page > 1) params.set('page', filters.page.toString());
  if (filters.limit && filters.limit !== 20) params.set('limit', filters.limit.toString());

  // Sıralama
  if (filters.sort) params.set('sort', filters.sort);

  // Kategori
  if (filters.category && filters.category.length > 0) {
    params.set('category', Array.isArray(filters.category) ? filters.category.join(',') : filters.category);
  }

  // Marka
  if (filters.brand && filters.brand.length > 0) {
    params.set('brand', Array.isArray(filters.brand) ? filters.brand.join(',') : filters.brand);
  }

  // Boyut
  // if (filters.size && filters.size.length > 0) {
  //   params.set('size', Array.isArray(filters.size) ? filters.size.join(',') : filters.size);
  // }
  //
  // // Renk
  // if (filters.color && filters.color.length > 0) {
  //   params.set('color', Array.isArray(filters.color) ? filters.color.join(',') : filters.color);
  // }

  // Fiyat
  if (filters.priceMin) params.set('price_min', filters.priceMin.toString());
  if (filters.priceMax) params.set('price_max', filters.priceMax.toString());

  // Stok
  if (filters.inStock !== undefined && filters.inStock !== null) params.set('in_stock', filters.inStock.toString());

  // İndirim
  if (filters.onSale !== undefined) params.set('on_sale', filters.onSale.toString());

  // Arama
  if (filters.query) params.set('q', filters.query);

  return params.toString();
};
