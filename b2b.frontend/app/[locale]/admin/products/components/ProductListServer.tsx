'use server';

import { Suspense } from 'react';
import ProductListClient from './ProductListClient';
import { ProductListDto } from '../types';
import api from '@/lib/api/client';

interface ProductListServerProps {
  canRead: boolean;
  canCreate: boolean;
  canUpdate: boolean;
  canDelete: boolean;
}

// This would typically fetch data from your API
async function getProducts(): Promise<ProductListDto[]> {
  try {
    // Try to get data from API using the existing api client
    const response = await api.get<ProductListDto[]>("/product");

    // Transform backend data to frontend format
    return response.map((product: ProductListDto) => ({
      id: product.id,
      name: product.name,
      slug: product.slug,
      description: product.description,
      productType: product.productType,
      category: product.category,
      brand: product.brand,
      sku: product.sku,
      barcode: product.barcode,
      price: product.price,
      discountedPrice: product.discountedPrice ?? product.price,
      stockQuantity: product.stockQuantity,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      variants: product.variants || [],
      variantCount: product.variants?.length || 0,
    }));
  } catch (error) {
    console.error("API çağrısı başarısız oldu", error);
    // If API call fails, return empty array
    return [];
  }
}

export default async function ProductListServer({ canRead, canCreate, canUpdate, canDelete }: ProductListServerProps) {
  // Veriyi doğrudan server'da çek
  const products = await getProducts();

  return (
    <Suspense fallback={<div>Loading products...</div>}>
      <ProductListClient products={products} canRead={canRead} canCreate={canCreate} canUpdate={canUpdate} canDelete={canDelete} />
    </Suspense>
  );
}
